/*
 * SOLID Principles Refactored MainRoutine
 *
 * This file has been refactored from the original monolithic MainRoutine to follow SOLID principles.
 *
 * INTEGRATION NOTES:
 * 1. Replace the 'dynamic' types in Globals with actual types from your codebase:
 *    - PLAYER should be the actual Player class type
 *    - CURRENT_ROUTINE should be the actual IRoutine interface type
 *    - GROUND_ITEMS should be List<GroundItem> with actual GroundItem type
 *    - MONOLITH_OBJECTIVES should be List<MonolithObjective> with actual type
 *
 * 2. Initialize Globals.MAIN_ROUTINE = new MainRoutine() when creating the routine
 *
 * 3. The null reference warnings can be resolved by:
 *    - Adding null checks where appropriate
 *    - Using nullable reference types properly
 *    - Ensuring global variables are initialized before use
 *
 * 4. This maintains the exact same behavior as the original while being more maintainable
 */


using System.Collections;
using Il2Cpp;
using MelonLoader;
using TestLE.Utilities;
using UnityEngine;
using UnityEngine.SceneManagement;
using Random = UnityEngine.Random;

namespace TestLE.Routine;

#region Core Interfaces and Orchestrator

/// <summary>
/// Defines the contract for any executable bot task.
/// This adheres to the Dependency Inversion Principle, as the main routine
/// will depend on this abstraction, not concrete implementations.
/// </summary>
public interface IGameTask
{
    /// <summary>
    /// Determines if this task can be executed in the current game state.
    /// </summary>
    /// <returns>True if the task can execute, otherwise false.</returns>
    bool CanExecute();

    /// <summary>
    /// The logic to be executed by the task.
    /// </summary>
    /// <returns>An IEnumerator for the game's coroutine scheduler.</returns>
    IEnumerator Execute();
}

/// <summary>
/// The main orchestrator that replaces the original monolithic class.
/// It manages and executes a prioritized list of tasks.
/// This class is OPEN for extension (by adding new IGameTask) but
/// CLOSED for modification.
/// </summary>
public class MainRoutine : IEnumerator
{
    public object? Current { get; private set; }

    private readonly List<IGameTask> _tasks;

    public MainRoutine()
    {
        // The order in this list defines the bot's decision-making priority.
        // High-priority tasks (like handling death) come first.
        _tasks = new List<IGameTask>
        {
            new HandleDeathTask(),
            new CompleteMonolithTask(this),
            new HandleLootTask(),
            new HandleInteractableTask(),
            new HandleGoodShrinesTask(),
            new HandleObjectiveTask(),
            new HandleCombatTask(this),
            new HandleIdleTask(this),
            new DefaultWanderTask() // Fallback task
        };
    }

    public bool MoveNext()
    {
        if (this != MAIN_ROUTINE)
        {
            MelonLogger.Msg("MainRoutine is not the current routine!");
            return false;
        }

        if (PLAYER == null)
        {
            MelonLogger.Msg("Player is null!");
            return false;
        }

        if (CURRENT_ROUTINE == null)
        {
            MelonLogger.Msg("Current routine is null!");
            return false;
        }

        // Find the first task that can be executed and run it.
        foreach (var task in _tasks.Where(task => task.CanExecute()))
        {
            Current = task.Execute();
            return true;
        }

        return false; // Should be unreachable if DefaultWanderTask exists
    }

    public void Reset()
    {
        Current = null;
    }

    // Expose internal state for tasks that need it
    internal float IdleTime { get; set; }
    internal Dictionary<Enemy, int> EnemyInactiveFailSafe { get; } = new();
    internal int CurrentStashTab { get; set; }
}

#endregion

// ---------------------------------------------------------------------------------

#region Specialized Handlers

/// <summary>
/// Single Responsibility: Manages all logic and state related to stashing items.
/// </summary>
public class StashHandler
{
    private readonly MainRoutine _mainRoutine;

    public StashHandler(MainRoutine mainRoutine)
    {
        _mainRoutine = mainRoutine;
    }

    public IEnumerator StashAllItems()
    {
        // Find stash opener, and move to it
        var stashOpener = FindHelpers.FindStashOpener();
        if (stashOpener == null)
        {
            MelonLogger.Msg("StashOpener not found!");
            yield break;
        }

        yield return PlayerHelpers.MoveToForce(stashOpener.transform.position);
        stashOpener.OnUse();
        yield return new WaitForSeconds(0.1f);

        // Find inventory UI, and move all items to stash
        var inventoryUI = FindHelpers.FindInventoryUI();
        if (inventoryUI == null)
        {
            MelonLogger.Msg("InventoryUI not found!");
            yield break;
        }

        if (inventoryUI.container == null)
        {
            MelonLogger.Msg("inventoryUI.container not found!");
            yield break;
        }

        var content = inventoryUI.container.GetContent();
        if (content == null)
        {
            MelonLogger.Msg("inventoryUI.container.GetContent() is null!");
            yield break;
        }

        var stashNavigable = FindHelpers.FindStashNavigable();
        if (stashNavigable == null)
        {
            MelonLogger.Msg("StashNavigable not found!");
            yield break;
        }

        var stashTabUI = FindHelpers.FindStashTabUI();
        if (stashTabUI == null)
        {
            MelonLogger.Msg("StashTabUI not found!");
            yield break;
        }

        yield return SelectCurrentStashTab(stashNavigable, stashTabUI);

        while (content.Count > 0)
        {
            var items = new List<ItemContainerEntry>();
            foreach (var i in content)
            {
                if (i == null)
                {
                    MelonLogger.Msg("Item is null!");
                    continue;
                }
                items.Add(i);
            }

            foreach (var i in items)
                inventoryUI.TryQuickMove(i.Position);

            yield return new WaitForSeconds(0.1f);

            if (content.Count == 0)
                break;

            MelonLogger.Msg("Stashing items failed, trying next stash tab!");
            yield return SelectNextStashTab(stashNavigable, stashTabUI);
        }

        yield return new WaitForSeconds(0.1f);
    }

    private IEnumerator SelectCurrentStashTab(StashTabsNavigable stashNavigable, StashTabbedUIControls stashTabUI)
    {
        stashTabUI.SwitchToTab(0);
        stashNavigable.ResetIndex();
        for (var i = 0; i < _mainRoutine.CurrentStashTab; i++)
            stashNavigable.IndexToRight();

        stashNavigable.ClickOnTab();
        yield return new WaitForSeconds(0.1f);
    }

    private IEnumerator SelectNextStashTab(StashTabsNavigable stashNavigable, StashTabbedUIControls stashTabUI)
    {
        _mainRoutine.CurrentStashTab++;
        stashNavigable.IndexToRight();
        stashNavigable.ClickOnTab();
        yield return new WaitForSeconds(0.1f);
    }
}
    
/// <summary>
/// Centralized location for coroutines used by multiple tasks.
/// </summary>
public static class SharedRoutines
{
    public static IEnumerator GoNextMonolith()
    {
        var stone = FindHelpers.FindMonolithStone();
        if (stone == null) 
            yield break;

        yield return PlayerHelpers.MoveToForce(stone.transform.position);
        stone.ObjectClick(PLAYER.gameObject, true);
        yield return new WaitForSeconds(1f);

        var islands = FindHelpers.FindMonolithIslands();
        if (islands.Count == 0) 
            yield break;

        foreach (var (_, ui) in islands)
        {
            if (ui.island.completed) 
                continue;
            
            if (ui.island.islandType is not (EchoWebIsland.IslandType.Normal or EchoWebIsland.IslandType.Arena or EchoWebIsland.IslandType.Beacon)) 
                continue;

            var hasConnectionWithCompleted = ui.island.connectedHexes.ToArray().Any(c => islands.GetValueOrDefault(c)?.island.completed ?? false);
            if (!hasConnectionWithCompleted)
                continue;
            
            ui.rightClicked();
            break;
        }

        while (SceneManager.GetActiveScene().name == "M_Rest")
            yield return new WaitForSeconds(1f);

        yield return new WaitForSeconds(1f);
        yield return CURRENT_ROUTINE!.OnNewArea();
    }
}

#endregion

// ---------------------------------------------------------------------------------

#region Game Task Implementations

/// <summary>
/// SRP: Checks for and handles the player's death.
/// </summary>
public class HandleDeathTask : IGameTask
{
    private DeathScreen? _deathScreen;

    public bool CanExecute()
    {
        _deathScreen = FindHelpers.FindDeathScreen();
        return _deathScreen != null && _deathScreen.isActiveAndEnabled;
    }

    public IEnumerator Execute()
    {
        MelonLogger.Msg("Death screen found!");
        _deathScreen!.NormalRespawnClick();
        yield return new WaitForSeconds(1f);
        yield return SharedRoutines.GoNextMonolith();
    }
}

/// <summary>
/// SRP: Handles the entire sequence of completing a monolith.
/// </summary>
public class CompleteMonolithTask : IGameTask
{
    private readonly StashHandler _stashHandler;

    public CompleteMonolithTask(MainRoutine mainRoutine)
    {
        _stashHandler = new StashHandler(mainRoutine);
    }

    public bool CanExecute() => FindHelpers.FindMonolithCompleteButton() != null;

    public IEnumerator Execute()
    {
        // Stop player by moving to the current position
        PlayerHelpers.MoveTo(PLAYER.transform.position);
        yield return new WaitForSeconds(1f);

        // Make portal
        PlayerHelpers.UsePortal();
        yield return new WaitForSeconds(2f);

        // Find portal
        var portal = FindHelpers.FindMonolithPortal();
        if (portal == null)
        {
            MelonLogger.Msg("Portal not found!");
            yield break;
        }

        // Wait for all ground items to be spawned
        if (LAST_GROUND_ITEM_DROP != DateTime.MinValue && (DateTime.Now - LAST_GROUND_ITEM_DROP).TotalSeconds < 1)
            yield return new WaitForSeconds(1f);

        // Loot all ground items before moving to portal
        while (GROUND_ITEMS.Count > 0)
        {
            yield return HandleLoot();
            yield return new WaitForSeconds(0.1f);
        }

        // Move to portal
        yield return PlayerHelpers.MoveToForce(portal.transform.position);

        // Click on portal
        portal.ObjectClick(PLAYER.gameObject, true);
        yield return new WaitForSeconds(1f);

        // Clear enemies
        ResetGlobals();

        // Click on reward chest
        var chest = FindHelpers.FindMonolithCompleteRewardChest();
        if (chest.obj != null && chest.isActive)
        {
            yield return PlayerHelpers.MoveToForce(chest.obj.transform.position);
            chest.obj.ObjectClick(PLAYER.gameObject, true);
            yield return new WaitForSeconds(1f);
        }
        else
        {
            MelonLogger.Msg("Chest not found!");
        }

        // Loot all ground items
        while (GROUND_ITEMS.Count > 0)
        {
            yield return HandleLoot();
            yield return new WaitForSeconds(0.1f);
        }

        // Click on reward rock
        var rock = FindHelpers.FindMonolithCompleteRewardRock();
        if (rock.obj != null && rock.isActive)
        {
            yield return PlayerHelpers.MoveToForce(rock.obj.transform.position);
            rock.obj.ObjectClick(PLAYER.gameObject, true);
            yield return new WaitForSeconds(1f);
        }
        else
        {
            MelonLogger.Msg("Rock not found!");
        }

        // Wait for 1 second, to allow the loot to finish spawning
        yield return new WaitForSeconds(1f);

        // Move and collect XP tomes
        var tomes = FindHelpers.FindGroundXPTomes();
        foreach (var t in tomes)
        {
            yield return PlayerHelpers.MoveToForce(t.transform.position);
        }

        // Loot all ground items
        while (GROUND_ITEMS.Count > 0)
        {
            yield return HandleLoot();
            yield return new WaitForSeconds(0.1f);
        }

        // Find stash opener, and move to it, then stash all items
        yield return _stashHandler.StashAllItems();

        // Move to next monolith
        yield return SharedRoutines.GoNextMonolith();
        MelonLogger.Msg("Monolith completed!");
    }

    public static IEnumerator HandleLoot()
    {
        MelonLogger.Msg("Handling loot!");
        var groundItem = GROUND_ITEMS.FirstOrDefault();
        if (groundItem == null)
        {
            MelonLogger.Msg("Ground item is null!");
            if (GROUND_ITEMS.Count > 0)
                GROUND_ITEMS.RemoveAt(0);
            yield break;
        }

        MelonLogger.Msg("Moving to ground item!");
        yield return groundItem.MoveToItem();
        groundItem.Pickup();
    }

    private IEnumerator CreateAndEnterPortal()
    {
        PlayerHelpers.UsePortal();
        yield return new WaitForSeconds(2f);
            
        var portal = FindHelpers.FindMonolithPortal();
        if (portal == null) yield break;
            
        yield return LootAllGroundItems(); // Loot before leaving
        yield return PlayerHelpers.MoveToForce(portal.transform.position);
        portal.ObjectClick(PLAYER.gameObject, true);
        yield return new WaitForSeconds(1f);
    }

    private IEnumerator OpenReward<T>(Func<(T obj, bool isActive)> findAction) where T : WorldObjectClickListener
    {
        var reward = findAction();
        if (reward.obj == null || !reward.isActive)
            yield break;
        
        yield return PlayerHelpers.MoveToForce(reward.obj.transform.position);
        reward.obj.ObjectClick(PLAYER.gameObject, true);
        yield return new WaitForSeconds(1.5f); // Wait for loot to drop
    }

    private IEnumerator LootAllGroundItems()
    {
        // Wait for any potential loot drops to finish spawning
        if (LAST_GROUND_ITEM_DROP != DateTime.MinValue && (DateTime.Now - LAST_GROUND_ITEM_DROP).TotalSeconds < 1)
            yield return new WaitForSeconds(1f);

        while (GROUND_ITEMS.Count > 0)
        {
            var groundItem = GROUND_ITEMS.FirstOrDefault();
            if (groundItem == null) { GROUND_ITEMS.RemoveAt(0); continue; }

            yield return groundItem.MoveToItem();
            groundItem.Pickup();
            yield return new WaitForSeconds(0.1f);
        }
    }

    private IEnumerator CollectXPTomes()
    {
        var tomes = FindHelpers.FindGroundXPTomes();
        foreach (var t in tomes)
        {
            yield return PlayerHelpers.MoveToForce(t.transform.position);
        }
    }
}
    
/// <summary>
/// SRP: Finds and picks up loot from the ground.
/// </summary>
public class HandleLootTask : IGameTask
{
    public bool CanExecute()
    {
        // Check if we are in range of loot, if so, loot
        var enemyResult = FindHelpers.FindNearestEnemy(PLAYER.transform.position, 100);
        return enemyResult.distance > 3f && GROUND_ITEMS.Count > 0;
    }

    public IEnumerator Execute()
    {
        yield return CompleteMonolithTask.HandleLoot();
    }
}

/// <summary>
/// SRP: Finds and interacts with world objects like interactables.
/// </summary>
public class HandleInteractableTask : IGameTask
{
    private WorldObjectClickListener? _targetInteractable;

    public bool CanExecute()
    {
        // Move to interactable and click it if it is within range
        for (var i = 0; i < INTERACTABLES.Count; i++)
        {
            var interactable = INTERACTABLES[i];
            if (interactable == null || !interactable.isActiveAndEnabled)
            {
                INTERACTABLES.RemoveAt(i);
                i--;
                continue;
            }

            var interactablePos = interactable.transform.position;
            if (Vector3.Distance(PLAYER.transform.position, interactablePos) > 20f)
                continue;

            _targetInteractable = interactable;
            return true;
        }
        return false;
    }

    public IEnumerator Execute()
    {
        yield return PlayerHelpers.MoveToForce(_targetInteractable!.transform.position, _targetInteractable.interactionRange * 0.8f);
        INTERACTABLES.Remove(_targetInteractable);
        _targetInteractable.ObjectClick(PLAYER.gameObject, true);
    }
}

/// <summary>
/// SRP: Handles good shrines before map objectives.
/// </summary>
public class HandleGoodShrinesTask : IGameTask
{
    public bool CanExecute()
    {
        return GOOD_SHRINES.Count > 0;
    }

    public IEnumerator Execute()
    {
        for (var i = 0; i < GOOD_SHRINES.Count; i++)
        {
            var s = GOOD_SHRINES[i];
            if (s == null)
            {
                GOOD_SHRINES.RemoveAt(i);
                i--;
            }
        }

        var playerPos = PLAYER.transform.position;
        GOOD_SHRINES.Sort((v1, v2) => Vector3.Distance(playerPos, v1!.transform.position).CompareTo(Vector3.Distance(playerPos, v2!.transform.position)));

        var shrine = GOOD_SHRINES.First();
        PlayerHelpers.MoveTo(shrine!.transform.position);
        yield return new WaitForSeconds(0.3333f);

        // ReSharper disable once Unity.InefficientPropertyAccess
        var distance = Vector3.Distance(PLAYER.transform.position, shrine.transform.position);
        if (distance <= shrine.interactionRange)
        {
            shrine.ObjectClick(PLAYER.gameObject, true);
            GOOD_SHRINES.Remove(shrine);
        }
    }
}
    
/// <summary>
/// SRP: Handles moving towards and completing monolith objectives.
/// </summary>
public class HandleObjectiveTask : IGameTask
{
    private MonolithObjective? _objective;

    public bool CanExecute()
    {
        if (MONOLITH_OBJECTIVES.Count == 0) return false;
        _objective = MONOLITH_OBJECTIVES.FirstOrDefault();
        return _objective != null;
    }

    public IEnumerator Execute()
    {
        var enemyObjective = _objective!.GetEnemyObjective();
        if (enemyObjective != null)
        {
            PlayerHelpers.MoveTo(enemyObjective.transform.position);
            yield return new WaitForSeconds(0.3333f);
            yield break;
        }

        var clickObjective = _objective.GetClickObjective();
        if (clickObjective != null)
        {
            var objectiveTransform = clickObjective.transform;
            var objectivePosition = objectiveTransform.position;

            PlayerHelpers.MoveTo(objectivePosition);
            yield return new WaitForSeconds(0.3333f);

            // Disable the warning because we waited 0.3333 seconds, which means we can't use the cached position of the objective, only the transform component
            // ReSharper disable once Unity.InefficientPropertyAccess
            var distance = Vector3.Distance(PLAYER.transform.position, objectiveTransform.position);
            if (distance <= clickObjective.interactionRange)
                clickObjective.ObjectClick(PLAYER.gameObject, true);
            yield break;
        }

        // MelonLogger.Msg("Objective not valid, defaulting to combat routine.");
        MONOLITH_OBJECTIVES.Remove(_objective);
    }
}

/// <summary>
/// SRP: Manages all combat logic.
/// </summary>
public class HandleCombatTask : IGameTask
{
    private Enemy? _targetEnemy;
    private float _distance;
    private readonly MainRoutine _mainRoutine;

    public HandleCombatTask(MainRoutine mainRoutine)
    {
        _mainRoutine = mainRoutine;
    }

    public bool CanExecute()
    {
        // Find nearest enemy
        var (enemy, distance) = FindHelpers.FindNearestEnemy(PLAYER.transform.position, 100);
        if (enemy == null) 
            return false;

        _targetEnemy = enemy;
        _distance = distance;

        // Check if we are in combat range, if so, do combat routine
        if (distance <= CURRENT_ROUTINE!.CombatDistance)
            return true;

        // If no objectives, default to combat
        return MONOLITH_OBJECTIVES.Count == 0;
    }

    public IEnumerator Execute()
    {
        // if (distance <= 1f && !enemy.Data.isActiveAndEnabled)
        if (_distance <= 3f && _targetEnemy!.Data.Data.actorName != "Exiled Mage" && (!_targetEnemy.Data.actorSync.gameObject.active || !_targetEnemy.Data.isActiveAndEnabled))
        {
            MelonLogger.Msg("Enemy is too close and not active!");
            _targetEnemy.RemoveEnemy();
            yield break;
        }
        
        if (_targetEnemy == null)
        {
            MelonLogger.Msg("Target enemy is null!");
            yield break;
        }

        var enemyTransform = _targetEnemy.Data.transform;
        if (enemyTransform == null)
        {
            MelonLogger.Msg("Enemy transform is null!");
            yield break;
        }

        if (_targetEnemy.Data.gameObject.active)
        {
            yield return CURRENT_ROUTINE!.Run(_targetEnemy, enemyTransform, _distance);
        }
        else
        {
            if (_distance <= 3f)
            {
                _mainRoutine.EnemyInactiveFailSafe[_targetEnemy] = _mainRoutine.EnemyInactiveFailSafe.GetValueOrDefault(_targetEnemy) + 1;
                if (_mainRoutine.EnemyInactiveFailSafe[_targetEnemy] >= 10)
                {
                    MelonLogger.Msg("Enemy is inactive for 10 tries, removing enemy!");
                    _targetEnemy.RemoveEnemy();
                    _mainRoutine.EnemyInactiveFailSafe.Remove(_targetEnemy);
                }
            }

            PlayerHelpers.MoveTo(enemyTransform.position);
            yield return new WaitForSeconds(0.3333f);
        }
    }
}

/// <summary>
/// SRP: Handles the case where the player is idle.
/// </summary>
public class HandleIdleTask : IGameTask
{
    private readonly MainRoutine _mainRoutine;
    private const float IDLE_SECONDS_THRESHOLD = 5f;

    public HandleIdleTask(MainRoutine mainRoutine)
    {
        _mainRoutine = mainRoutine;
    }

    public bool CanExecute()
    {
        // Move to random position if idle for 5 seconds
        if (PLAYER.movingState.myAgent.velocity.magnitude <= 0.1f)
        {
            _mainRoutine.IdleTime += Time.deltaTime;
            if (_mainRoutine.IdleTime >= IDLE_SECONDS_THRESHOLD)
                return true;
        }
        else
        {
            _mainRoutine.IdleTime = 0;
        }
        return false;
    }

    public IEnumerator Execute()
    {
        _mainRoutine.IdleTime = 0;
        if (UnityHelpers.RandomPointOnNavMesh(PLAYER.transform.position, 10f, out Vector3 movePos))
            PlayerHelpers.MoveTo(movePos);
        else
            PlayerHelpers.MoveTo(PLAYER.transform.position + new Vector3(Random.Range(-50, 50), 0, Random.Range(-50, 50)));

        yield return new WaitForSeconds(2f);
    }
}

/// <summary>
/// SRP: A fallback task to ensure the bot is always doing something.
/// </summary>
public class DefaultWanderTask : IGameTask
{
    public bool CanExecute()
    {
        // Find nearest enemy
        var enemyResult = FindHelpers.FindNearestEnemy(PLAYER.transform.position, 100);
        return enemyResult.enemy == null;
        // No mobs found, wander
    }

    public IEnumerator Execute()
    {
        MelonLogger.Msg("No mobs found!");
        PlayerHelpers.MoveTo(PLAYER.transform.position + new Vector3(Random.Range(-5, 5), 0, Random.Range(-5, 5)));
        yield return new WaitForSeconds(1f);
    }
}
#endregion